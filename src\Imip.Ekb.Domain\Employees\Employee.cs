using System;
using System.ComponentModel.DataAnnotations;
using Volo.Abp;
using Volo.Abp.Domain.Entities.Auditing;

namespace Imip.Ekb.Employees;

public class Employee : FullAuditedAggregateRoot<Guid>
{
    [Required]
    [StringLength(50)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string LastName { get; set; } = string.Empty;

    [Required]
    [EmailAddress]
    [StringLength(100)]
    public string Email { get; set; } = string.Empty;

    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    [Required]
    [StringLength(100)]
    public string Department { get; set; } = string.Empty;

    [StringLength(100)]
    public string? Position { get; set; }

    public DateTime HireDate { get; set; }

    [Range(0, double.MaxValue)]
    public decimal Salary { get; set; }

    public string? PhotoPath { get; set; }

    public bool IsActive { get; set; } = true;

    public string FullName => $"{FirstName} {LastName}";

    protected Employee()
    {
        // For EF Core
    }

    public Employee(
        Guid id,
        string firstName,
        string lastName,
        string email,
        string department,
        string? position,
        DateTime hireDate,
        decimal salary,
        string? phoneNumber = null,
        string? photoPath = null) : base(id)
    {
        SetName(firstName, lastName);
        SetEmail(email);
        Department = department;
        Position = position;
        HireDate = hireDate;
        Salary = salary;
        PhoneNumber = phoneNumber;
        PhotoPath = photoPath;
    }

    public void SetName(string firstName, string lastName)
    {
        FirstName = Check.NotNullOrWhiteSpace(firstName, nameof(firstName), maxLength: 50);
        LastName = Check.NotNullOrWhiteSpace(lastName, nameof(lastName), maxLength: 50);
    }

    public void SetEmail(string email)
    {
        Email = Check.NotNullOrWhiteSpace(email, nameof(email), maxLength: 100);
    }

    public void UpdateDetails(
        string firstName,
        string lastName,
        string email,
        string department,
        string? position,
        DateTime hireDate,
        decimal salary,
        string? phoneNumber = null,
        string? photoPath = null)
    {
        SetName(firstName, lastName);
        SetEmail(email);
        Department = department;
        Position = position;
        HireDate = hireDate;
        Salary = salary;
        PhoneNumber = phoneNumber;
        PhotoPath = photoPath;
    }

    public void Activate()
    {
        IsActive = true;
    }

    public void Deactivate()
    {
        IsActive = false;
    }
}