using System;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Domain.Services;

namespace Imip.Ekb.Employees;

public class EmployeeManager : DomainService
{
    private readonly IEmployeeRepository _employeeRepository;

    public EmployeeManager(IEmployeeRepository employeeRepository)
    {
        _employeeRepository = employeeRepository;
    }

    public async Task<Employee> CreateAsync(
        string firstName,
        string lastName,
        string email,
        string department,
        string? position,
        DateTime hireDate,
        decimal salary,
        string? phoneNumber = null,
        string? photoPath = null)
    {
        Check.NotNullOrWhiteSpace(firstName, nameof(firstName));
        Check.NotNullOrWhiteSpace(lastName, nameof(lastName));
        Check.NotNullOrWhiteSpace(email, nameof(email));
        Check.NotNullOrWhiteSpace(department, nameof(department));

        await CheckEmailUniqueAsync(email);

        if (salary < 0)
        {
            throw new UserFriendlyException("Salary cannot be negative.");
        }

        if (hireDate > DateTime.Now)
        {
            throw new UserFriendlyException("Hire date cannot be in the future.");
        }

        return new Employee(
            GuidGenerator.Create(),
            firstName,
            lastName,
            email,
            department,
            position,
            hireDate,
            salary,
            phoneNumber,
            photoPath);
    }

    public async Task UpdateAsync(
        Employee employee,
        string firstName,
        string lastName,
        string email,
        string department,
        string? position,
        DateTime hireDate,
        decimal salary,
        string? phoneNumber = null,
        string? photoPath = null)
    {
        Check.NotNull(employee, nameof(employee));
        Check.NotNullOrWhiteSpace(firstName, nameof(firstName));
        Check.NotNullOrWhiteSpace(lastName, nameof(lastName));
        Check.NotNullOrWhiteSpace(email, nameof(email));
        Check.NotNullOrWhiteSpace(department, nameof(department));

        if (employee.Email != email)
        {
            await CheckEmailUniqueAsync(email, employee.Id);
        }

        if (salary < 0)
        {
            throw new UserFriendlyException("Salary cannot be negative.");
        }

        if (hireDate > DateTime.Now)
        {
            throw new UserFriendlyException("Hire date cannot be in the future.");
        }

        employee.UpdateDetails(
            firstName,
            lastName,
            email,
            department,
            position,
            hireDate,
            salary,
            phoneNumber,
            photoPath);
    }

    private async Task CheckEmailUniqueAsync(string email, Guid? excludeId = null)
    {
        var isUnique = await _employeeRepository.IsEmailUniqueAsync(email, excludeId);
        if (!isUnique)
        {
            throw new UserFriendlyException($"An employee with email '{email}' already exists.");
        }
    }
}
